# 来料检验看板接口适配说明

## 🔗 接口配置

### 1. 接口地址配置

```typescript
const API_BASE = '/inja-qms-tznq/v1';
const getOrganizationId = () => getCurrentOrganizationId() || '25018';
```

### 2. 已适配的接口

#### 2.1 饼状图进度统计接口
- **接口地址**: `/inja-qms-tznq/v1/{organizationId}/qms-inspect-iqc-dashboards/progress-stats/ui`
- **请求方式**: GET
- **请求参数**:
  ```typescript
  {
    startDate: "2025-06-01 00:00:00",
    endDate: "2025-07-30 23:59:59"
  }
  ```
- **返回数据结构**:
  ```typescript
  {
    pending: 16,      // 待检验
    overdue: 0,       // 超期待检
    inProgress: 11,   // 检验中
    completed: 11     // 检验完成
  }
  ```

#### 2.2 不良统计Top5接口
- **接口地址**: `/inja-qms-tznq/v1/{organizationId}/qms-inspect-iqc-dashboards/defective-stats/ui`
- **请求方式**: GET
- **请求参数**:
  ```typescript
  {
    startDate: "2025-06-01 00:00:00",
    endDate: "2025-07-30 23:59:59"
  }
  ```
- **返回数据结构**:
  ```typescript
  [
    {
      name: "外观",
      count: 6,
      ratio: 42.85
    },
    {
      name: "感官", 
      count: 2,
      ratio: 14.28
    }
    // ... 更多项目
  ]
  ```

#### 2.3 物料统计分页接口
- **接口地址**: `/inja-qms-tznq/v1/{organizationId}/qms-inspect-iqc-dashboards/material-stats/ui`
- **请求方式**: GET
- **请求参数**:
  ```typescript
  {
    startDate: "2025-06-01 00:00:00",
    endDate: "2025-07-30 23:59:59",
    supplierId?: 4332001,    // 可选，筛选特定供应商
    size: 20,                // 页大小
    page: 0                  // 页码（从0开始）
  }
  ```
- **返回数据结构**:
  ```typescript
  {
    totalPages: 1,
    totalElements: 7,
    numberOfElements: 7,
    size: 20,
    number: 0,
    content: [
      {
        materialId: 2118049001,
        materialCode: "70900600100000000",
        material: "白糖",
        arrivalBatchCount: 16,
        totalInspections: 16,
        passedInspections: 0,
        passRateNum: 0,
        passRate: "0.00%"
      }
      // ... 更多记录
    ],
    empty: false
  }
  ```

#### 2.4 供应商统计分页接口
- **接口地址**: `/inja-qms-tznq/v1/{organizationId}/qms-inspect-iqc-dashboards/supplier-stats/ui`
- **请求方式**: GET
- **请求参数**:
  ```typescript
  {
    startDate: "2025-06-01 00:00:00",
    endDate: "2025-07-30 23:59:59",
    materialId?: 2119734001, // 可选，筛选特定物料
    size: 20,                // 页大小
    page: 0                  // 页码（从0开始）
  }
  ```
- **返回数据结构**:
  ```typescript
  {
    totalPages: 1,
    totalElements: 7,
    numberOfElements: 7,
    size: 20,
    number: 0,
    content: [
      {
        supplierId: 4332001,
        supplierCode: "A216521366",
        supplier: "云南建投物流有限公司",
        arrivalBatchCount: 13,
        totalInspections: 13,
        passedInspections: 1,
        passRateNum: 7.69,
        passRate: "7.69%"
      }
      // ... 更多记录
    ],
    empty: false
  }
  ```

#### 2.5 不良明细分页接口
- **接口地址**: `/inja-qms-tznq/v1/{organizationId}/qms-inspect-iqc-dashboards/defective-stats/details/ui`
- **请求方式**: GET
- **请求参数**:
  ```typescript
  {
    startDate: "2025-06-01 00:00:00",
    endDate: "2025-07-30 23:59:59",
    defectiveItem?: "外观",  // 可选，筛选特定不良项目
    size: 20,               // 页大小
    page: 0                 // 页码（从0开始）
  }
  ```
- **返回数据结构**:
  ```typescript
  {
    totalPages: 1,
    totalElements: 6,
    numberOfElements: 6,
    size: 20,
    number: 0,
    content: [
      {
        defectiveItem: "外观",
        inspectionId: "IDIQC20250619354",
        inspectDocId: 11088001,
        materialId: 2119702001,
        materialCode: "7160020000000004",
        material: "纸盒",
        supplierId: 4445001,
        supplierCode: "A719416197",
        supplier: "云南玉溪玉昆钢铁集团有限公司",
        inspector: null,
        inspectorId: 41437,
        creationDate: "2025-06-19 11:33:29"
      }
      // ... 更多记录
    ],
    empty: false
  }
  ```

## 🔄 数据适配处理

### 1. 饼状图数据适配

```typescript
const pieChartData = useMemo(() => {
  return [
    { name: '检验完成', value: progressStats.completed, itemStyle: { color: '#4a90e2' } },
    { name: '待检验', value: progressStats.pending, itemStyle: { color: '#f5a623' } },
    { name: '超期待检', value: progressStats.overdue, itemStyle: { color: '#d0021b' } },
    { name: '检验中', value: progressStats.inProgress, itemStyle: { color: '#50e3c2' } },
  ].sort((a, b) => a.value - b.value);
}, [progressStats]);
```

### 2. 不良统计数据适配

```typescript
const defectiveStatsData = useMemo(() => {
  // 使用真实的接口数据，取前5项
  const topDefectiveData = defectiveStats.slice(0, 5);

  // 在前后添加透明的占位数据
  const categories = ['', ...topDefectiveData.map(s => s.name), ''];
  const counts = [
    { value: 0, itemStyle: { color: 'transparent' } }, // 透明占位
    ...topDefectiveData.map(s => s.count),
    { value: 0, itemStyle: { color: 'transparent' } }, // 透明占位
  ];
  const ratios = [
    null, // 不显示折线图点
    ...topDefectiveData.map(s => s.ratio / 100), // 将百分比转换为小数
    null, // 不显示折线图点
  ];

  return {
    categories,
    counts,
    ratios,
  };
}, [defectiveStats]);
```

### 3. 物料统计数据适配

```typescript
// 物料数据获取和分页处理
const loadMoreMaterialData = async () => {
  if (materialPageInfo.loading || !materialPageInfo.hasMore) return;

  setMaterialPageInfo(prev => ({ ...prev, loading: true }));

  try {
    // 调用实际的API
    const response = await fetchMaterialStats(
      startDate.format('YYYY-MM-DD'),
      endDate.format('YYYY-MM-DD'),
      selectedSupplier?.get('id'), // 如果选择了供应商，则筛选
      materialPageInfo.current,
      materialPageInfo.pageSize,
    );

    // 转换数据格式并标记新加载的数据
    const newData = response.content.map(item => ({
      ...item,
      isNewlyLoaded: true,
    }));

    setMaterialPageData(prev => [...prev, ...newData]);
    setMaterialPageInfo(prev => ({
      ...prev,
      current: prev.current + 1,
      loading: false,
      hasMore: response.number < response.totalPages - 1,
      total: response.totalElements,
    }));

    // 移除新加载标记，触发动画
    setTimeout(() => {
      setMaterialPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
    }, 100);
  } catch (error) {
    console.error('加载物料数据失败:', error);
    setMaterialPageInfo(prev => ({ ...prev, loading: false }));
  }
};
```

### 4. 供应商统计数据适配

```typescript
// 供应商数据获取和分页处理
const loadMoreSupplierData = async () => {
  if (supplierPageInfo.loading || !supplierPageInfo.hasMore) return;

  setSupplierPageInfo(prev => ({ ...prev, loading: true }));

  try {
    // 调用实际的API
    const response = await fetchSupplierStats(
      startDate.format('YYYY-MM-DD'),
      endDate.format('YYYY-MM-DD'),
      selectedMaterial?.get('id'), // 如果选择了物料，则筛选
      supplierPageInfo.current,
      supplierPageInfo.pageSize,
    );

    // 转换数据格式并标记新加载的数据
    const newData = response.content.map(item => ({
      ...item,
      isNewlyLoaded: true,
    }));

    setSupplierPageData(prev => [...prev, ...newData]);
    setSupplierPageInfo(prev => ({
      ...prev,
      current: prev.current + 1,
      loading: false,
      hasMore: response.number < response.totalPages - 1,
      total: response.totalElements,
    }));

    // 移除新加载标记，触发动画
    setTimeout(() => {
      setSupplierPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
    }, 100);
  } catch (error) {
    console.error('加载供应商数据失败:', error);
    setSupplierPageInfo(prev => ({ ...prev, loading: false }));
  }
};
```

### 5. 不良明细数据适配

```typescript
// 转换数据格式以匹配现有的显示结构
const newData = response.content.map((item, index) => ({
  id: defectivePageInfo.current * defectivePageInfo.pageSize + index + 1,
  inspectionId: item.inspectionId,
  materialCode: item.materialCode,
  material: item.material,
  supplier: item.supplier,
  supplierCode: item.supplierCode,
  creationDate: item.creationDate,
  status: '检验完成',
  isPassed: false,
  defectiveItem: item.defectiveItem,
  inspector: item.inspector || '未知',
  arrivalBatch: `B${String(item.inspectDocId).slice(-3)}`,
  overdueDays: 0,
  isNewlyLoaded: true, // 标记新加载的数据
}));
```

## 📊 数据获取时机

### 1. 初始化数据获取

```typescript
// 获取接口数据
useEffect(() => {
  const fetchData = async () => {
    try {
      const startDateStr = startDate.format('YYYY-MM-DD');
      const endDateStr = endDate.format('YYYY-MM-DD');
      
      // 获取进度统计数据
      const progressData = await fetchProgressStats(startDateStr, endDateStr);
      setProgressStats(progressData);
      
      // 获取不良统计数据
      const defectiveData = await fetchDefectiveStats(startDateStr, endDateStr);
      setDefectiveStats(defectiveData);

      // 获取物料统计数据（第一页）
      const materialData = await fetchMaterialStats(startDateStr, endDateStr, undefined, 0, 20);
      setMaterialStats(materialData.content);
      setMaterialPageData(materialData.content);
      setMaterialPageInfo({
        current: 1,
        pageSize: 20,
        total: materialData.totalElements,
        hasMore: materialData.number < materialData.totalPages - 1,
        loading: false,
      });

      // 获取供应商统计数据（第一页）
      const supplierData = await fetchSupplierStats(startDateStr, endDateStr, undefined, 0, 20);
      setSupplierStats(supplierData.content);
      setSupplierPageData(supplierData.content);
      setSupplierPageInfo({
        current: 1,
        pageSize: 20,
        total: supplierData.totalElements,
        hasMore: supplierData.number < supplierData.totalPages - 1,
        loading: false,
      });

    } catch (error) {
      console.error('获取数据失败:', error);
    }
  };
  
  fetchData();
}, [startDate, endDate]);
```

### 2. 分页数据获取

- **触发时机**: 滚动到表格底部时自动触发
- **加载方式**: 异步加载，支持平滑动画
- **数据合并**: 新数据追加到现有数据后面

## 🎯 接口状态管理

### 1. 数据状态

```typescript
// 接口数据状态
const [progressStats, setProgressStats] = useState<ProgressStatsData>({
  pending: 0,
  overdue: 0,
  inProgress: 0,
  completed: 0,
});
const [defectiveStats, setDefectiveStats] = useState<DefectiveStatsData[]>([]);
const [materialStats, setMaterialStats] = useState<MaterialStatsData[]>([]);
const [supplierStats, setSupplierStats] = useState<SupplierStatsData[]>([]);
```

### 2. 分页状态

```typescript
const [defectivePageInfo, setDefectivePageInfo] = useState<{
  current: number;
  pageSize: number;
  total: number;
  hasMore: boolean;
  loading: boolean;
}>({
  current: 1,
  pageSize: 20,
  total: 0,
  hasMore: true,
  loading: false,
});
```

## ✅ 适配完成情况

### 已完成 ✅
- [x] 饼状图进度统计接口适配
- [x] 不良统计Top5接口适配
- [x] 不良明细分页接口适配
- [x] 物料统计分页接口适配
- [x] 供应商统计分页接口适配
- [x] 数据格式转换和适配
- [x] 错误处理和状态管理
- [x] 分页滚动加载功能

### 待完成 ⏳
- [ ] 筛选条件联动优化（物料、供应商筛选）
- [ ] 数据刷新机制优化
- [ ] 实时数据更新功能

## 🔧 使用说明

1. **时间范围变更**: 修改开始/结束时间后，会自动重新获取所有数据（进度统计、不良统计、物料统计、供应商统计）
2. **分页加载**: 三个表格（不良明细、物料统计、供应商统计）都支持滚动到底部时自动加载下一页数据
3. **数据动画**: 新加载的数据会有淡入动画效果
4. **筛选联动**:
   - 选择供应商后，物料统计会根据该供应商进行筛选
   - 选择物料后，供应商统计会根据该物料进行筛选
5. **错误处理**: 接口调用失败时会在控制台输出错误信息

## 📝 注意事项

1. **组织ID**: 当前硬编码为 `25018`，实际使用时会从 `getCurrentOrganizationId()` 获取
2. **时间格式**: 接口要求时间格式为 `YYYY-MM-DD HH:mm:ss`
3. **分页参数**: 后端分页从0开始，前端需要注意转换
4. **数据转换**: 百分比数据需要除以100转换为小数用于图表显示

## 🔗 **筛选联动机制**

### 物料-供应商筛选联动
1. **选择供应商**: 当用户在供应商筛选中选择特定供应商时，物料统计表格会自动筛选该供应商相关的物料数据
2. **选择物料**: 当用户在物料筛选中选择特定物料时，供应商统计表格会自动筛选该物料相关的供应商数据
3. **参数传递**:
   - `fetchMaterialStats(startDate, endDate, supplierId)` - 传入供应商ID进行筛选
   - `fetchSupplierStats(startDate, endDate, materialId)` - 传入物料ID进行筛选

现在所有五个接口都已经成功适配真实接口数据！🎉
- ✅ 饼状图进度统计
- ✅ 不良统计Top5
- ✅ 不良明细分页
- ✅ 物料统计分页
- ✅ 供应商统计分页

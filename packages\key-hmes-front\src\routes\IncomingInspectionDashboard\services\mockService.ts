/*
 * @Description: 来料检验看板Mock服务 - 用于开发环境
 */
import {
  ProgressStatsData,
  DefectiveStatsData,
  DefectiveDetailResponse,
  MaterialStatsResponse,
  SupplierStatsResponse,
} from '.';
import {
  mockProgressStats,
  mockDefectiveStats,
  mockDefectiveDetails,
  mockMaterialStats,
  mockSupplierStats,
} from './mockData';

// 模拟网络延迟
const delay = (ms: number = 800) => new Promise(resolve => setTimeout(resolve, ms));

// Mock进度统计接口
export const mockFetchProgressStats = async (
  startDate: string,
  endDate: string,
): Promise<ProgressStatsData> => {
  await delay();
  console.log('Mock API: fetchProgressStats', { startDate, endDate });
  return mockProgressStats;
};

// Mock不良统计接口
export const mockFetchDefectiveStats = async (
  startDate: string,
  endDate: string,
): Promise<DefectiveStatsData[]> => {
  await delay();
  console.log('Mock API: fetchDefectiveStats', { startDate, endDate });
  return mockDefectiveStats;
};

// Mock不良明细接口
export const mockFetchDefectiveDetails = async (
  startDate: string,
  endDate: string,
  defectiveItem?: string,
  pageNum: number = 0,
  pageSize: number = 20,
): Promise<DefectiveDetailResponse> => {
  await delay();
  console.log('Mock API: fetchDefectiveDetails', {
    startDate,
    endDate,
    defectiveItem,
    pageNum,
    pageSize,
  });

  // 模拟分页逻辑
  const allData = [...mockDefectiveDetails.content];

  // 如果指定了不良项目，进行筛选
  let filteredData = allData;
  if (defectiveItem) {
    filteredData = allData.filter(item => item.defectiveItem === defectiveItem);
  }

  // 生成更多假数据用于分页测试
  const extendedData = [];
  for (let i = 0; i < 50; i++) {
    filteredData.forEach((item, index) => {
      extendedData.push({
        ...item,
        inspectionId: `${item.inspectionId}_${i}_${index}`,
        inspectDocId: item.inspectDocId + i * 1000 + index,
        creationDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
          .toISOString()
          .slice(0, 19)
          .replace('T', ' '),
      });
    });
  }

  const startIndex = pageNum * pageSize;
  const endIndex = startIndex + pageSize;
  const pageData = extendedData.slice(startIndex, endIndex);

  return {
    totalPages: Math.ceil(extendedData.length / pageSize),
    totalElements: extendedData.length,
    numberOfElements: pageData.length,
    size: pageSize,
    number: pageNum,
    content: pageData,
    empty: pageData.length === 0,
  };
};

// Mock物料统计接口
export const mockFetchMaterialStats = async (
  startDate: string,
  endDate: string,
  supplierId?: number,
  pageNum: number = 0,
  pageSize: number = 20,
): Promise<MaterialStatsResponse> => {
  await delay();
  console.log('Mock API: fetchMaterialStats', {
    startDate,
    endDate,
    supplierId,
    pageNum,
    pageSize,
  });

  // 模拟分页逻辑
  let allData = [...mockMaterialStats.content];

  // 如果指定了供应商ID，进行筛选（这里简单模拟）
  if (supplierId) {
    // 模拟筛选逻辑，实际应该根据供应商ID筛选相关物料
    allData = allData.filter((_, index) => index % 2 === 0); // 简单的模拟筛选
  }

  // 生成更多假数据用于分页测试
  const extendedData = [];
  for (let i = 0; i < 3; i++) {
    allData.forEach((item, index) => {
      extendedData.push({
        ...item,
        materialId: item.materialId + i * 1000000 + index,
        materialCode: `${item.materialCode}_${i}_${index}`,
        material: `${item.material}_批次${i + 1}`,
        arrivalBatchCount: item.arrivalBatchCount + Math.floor(Math.random() * 10),
        totalInspections: item.totalInspections + Math.floor(Math.random() * 10),
        passedInspections: Math.floor(Math.random() * item.totalInspections),
      });
    });
  }

  // 重新计算合格率
  extendedData.forEach(item => {
    item.passRateNum =
      item.totalInspections > 0 ? (item.passedInspections / item.totalInspections) * 100 : 0;
    item.passRate = `${item.passRateNum.toFixed(2)}%`;
  });

  const startIndex = pageNum * pageSize;
  const endIndex = startIndex + pageSize;
  const pageData = extendedData.slice(startIndex, endIndex);

  return {
    totalPages: Math.ceil(extendedData.length / pageSize),
    totalElements: extendedData.length,
    numberOfElements: pageData.length,
    size: pageSize,
    number: pageNum,
    content: pageData,
    empty: pageData.length === 0,
  };
};

// Mock供应商统计接口
export const mockFetchSupplierStats = async (
  startDate: string,
  endDate: string,
  materialId?: number,
  pageNum: number = 0,
  pageSize: number = 20,
): Promise<SupplierStatsResponse> => {
  await delay();
  console.log('Mock API: fetchSupplierStats', {
    startDate,
    endDate,
    materialId,
    pageNum,
    pageSize,
  });

  // 模拟分页逻辑
  let allData = [...mockSupplierStats.content];

  // 如果指定了物料ID，进行筛选（这里简单模拟）
  if (materialId) {
    // 模拟筛选逻辑，实际应该根据物料ID筛选相关供应商
    allData = allData.filter((_, index) => index % 3 !== 0); // 简单的模拟筛选
  }

  // 生成更多假数据用于分页测试
  const extendedData = [];
  for (let i = 0; i < 2; i++) {
    allData.forEach((item, index) => {
      extendedData.push({
        ...item,
        supplierId: item.supplierId + i * 1000000 + index,
        supplierCode: `${item.supplierCode}_${i}_${index}`,
        supplier: `${item.supplier}_分公司${i + 1}`,
        arrivalBatchCount: item.arrivalBatchCount + Math.floor(Math.random() * 5),
        totalInspections: item.totalInspections + Math.floor(Math.random() * 5),
        passedInspections: Math.floor(Math.random() * item.totalInspections),
      });
    });
  }

  // 重新计算合格率
  extendedData.forEach(item => {
    item.passRateNum =
      item.totalInspections > 0 ? (item.passedInspections / item.totalInspections) * 100 : 0;
    item.passRate = `${item.passRateNum.toFixed(2)}%`;
  });

  const startIndex = pageNum * pageSize;
  const endIndex = startIndex + pageSize;
  const pageData = extendedData.slice(startIndex, endIndex);

  return {
    totalPages: Math.ceil(extendedData.length / pageSize),
    totalElements: extendedData.length,
    numberOfElements: pageData.length,
    size: pageSize,
    number: pageNum,
    content: pageData,
    empty: pageData.length === 0,
  };
};
